<script setup>
import { ref, onMounted, provide, watch } from "vue";
import Axios from "axios";
import PageOne from "../lp17/components/PageOne.vue";
import PageTwo from "../lp17/components/PageTwo.vue";
import PageThree from "../lp17/components/PageThree.vue";
import PageFour from "../lp17/components/PageFour.vue";
import PageFive from "../lp17/components/PageFive.vue";
import PageSix from "../lp17/components/PageSix.vue";
import PageSeven from "../lp17/components/PageSeven.vue";
import PageEight from "../lp17/components/PageEight.vue";
import Navbar from "../lp17/components/Navbar.vue";
import Footer from "../../components/Footer12.vue";
import Background from "../../components/Background.vue";
import Endpoints from "./../../assets/js/config/endpoints.json";
import Params from "./../../assets/js/helper/urlParameters.js";
import Media from "./../../assets/js/helper/deviceDetection.js";
import Language from "./../../assets/js/helper/Language.js";
import inputData from "./../../assets/js/config/forms.json";
import config from "../../assets/js/config/config.json";
import Assets from "../../assets/js/config/config.json";

const cdn = Assets.cdn;
// const cardImage = "image" in Params().raw ? `${Assets.images.img}${Params().raw["image"]}.webp` : `${Assets.images.img}1.webp`;

const location = ref();
const device = ref(false);
const steps = ref(1);
const hasEmailParam = ref(false);

provide("assets", config.assets);

onMounted(async () => {
  const urlParams = new URLSearchParams(window.location.search);
  const emailParam = urlParams.get("email");

  hasEmailParam.value = !!emailParam;
  if (hasEmailParam.value) {
    inputData.email = {
      value: emailParam,
      valid: true,
      error: false,
      required: true,
    };
  }
  await Axios.get(Endpoints.geo).then((s) => {
    device.value = s.data;
    inputData.location.value = s.data.geo.city ? s.data.geo.city : "Nevada";
    inputData.device.value = s.data;
    inputData.lander.value = Params().path;
    inputData.click_id.value = Params().click_id;
    inputData.source_id.value = Params().source_id;
    inputData.country.value = s.data.geo.country;
    inputData.country_code.value = s.data.geo.country_code.toLowerCase();
    inputData.locale.value = Params().locale;
    inputData.media.value = Media().device;
    location.value = inputData.location.value;
    inputData.http.value = Params().http;
    inputData.t1.value = Params().t1;
    inputData.t2.value = Params().t2;
    inputData.l.value = Params().l;
    inputData.s3.value = Params().s3;
    inputData.cmp.value = Params().cmp;
    inputData.image.value = Params().image;
    inputData.traf_id.value = Params().traf_id;
inputData.tsid.value = Params().tsid;
inputData.alreadyRegistered.value = Params().alreadyRegistered;
    if (s.data.geo.postal_code) {
      inputData.postal_code.value = s.data.geo.postal_code;
    }
  });
  window.history.pushState({ page: 1 }, null);
  window.addEventListener("popstate", handlePopstate);

  function handlePopstate() {
    window.location.href = config.links.back_button + Params().url;
  }
});

const moveNextSlide = () => {
  if (hasEmailParam.value && steps.value === 5) {
    steps.value = 7;
  } else {
    steps.value++;
  }
};

const moveBackSlide = () => {
  if (hasEmailParam.value && steps.value === 7) {
    steps.value = 5;
  } else {
    steps.value--;
  }
};

watch(steps, (newValue) => {
  console.log("Step changed to:", newValue);
});
</script>

<template>
  <div :style="{ backgroundImage: `url(${cdn})`, backgroundSize: 'cover', backgroundPosition: 'center', backgroundRepeat: 'no-repeat' }">
    <Background />
    <div class="layoutlp9">
      <Navbar />
      <div class="d-flex justify-content-center align-items-center lp12" style="height: 100vh">
        <!-- Steps -->
        <div class="bg-transparent">
          <div v-if="steps === 1">
            <PageOne :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
          </div>
          <div v-else-if="steps === 2">
            <PageTwo :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
          </div>
          <div v-else-if="steps === 3">
            <PageThree :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
          </div>
          <div v-else-if="steps === 4">
            <PageFour :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
          </div>
          <div v-else-if="steps === 5">
            <PageFive :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
          </div>
          <div v-else-if="steps === 6 && !hasEmailParam">
            <PageSix :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
          </div>
          <div v-else-if="steps === 7 || (steps === 6 && hasEmailParam)">
            <PageSeven :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
          </div>
          <div v-else-if="steps === 8">
            <PageEight :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
          </div>
        </div>
      </div>
      <Footer :language="Language" />
    </div>
  </div>
</template>

<style scoped>
.card-img {
  display: none;
}

.lp12 {
  margin-left: 0;
}

@media (min-width: 992px) {
  .lp12 {
    /* margin-left: 8em !important; */
    /* display: flex !important; */
    /* justify-content: start !important; */
  }
}
</style>
