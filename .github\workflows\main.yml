name: <PERSON><PERSON> and <PERSON><PERSON>niff

on:
  push:
    branches:
      - voluum # Change this to your default branch

jobs:
  lint-and-codesniff:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: 18 # Change this to the Node.js version you need

    - name: Install dependencies
      run: npm install

    - name: Run ESLint
      run: npm run lint # Replace with your ESLint command

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: 8.2.9 # Change this to the PHP version you need

    - name: Install Composer dependencies
      run: composer install

    - name: Run PHP CodeSniffer
      run: |
        chmod +x vendor/bin/phpcs
        vendor/bin/phpcs api/ --standard=phpcs.xml

    - name: Upload reports
      uses: actions/upload-artifact@v2
      with:
        name: reports
        path: $GITHUB_WORKSPACE/reports
