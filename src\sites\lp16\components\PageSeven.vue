<script setup>
import { ref, onMounted, inject } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Api from "../../../assets/js/helper/api.js";
import Swal from "sweetalert2";
import Searching from "../../lp14/components/views/Searching.vue";

const props = defineProps({
  inputs: { required: true },
  steps: { required: true },
  moveNextSlide: { required: true },
});
const steps = ref(props.steps);
const apiErrorMessage = ref(false);
const disableSubmitBtn = ref(false);
const assets = inject("assets");
const showSearching = ref(false);

const validationMessages = ref({
  username: false,
  dob: false,
  email: false,
  password: false,
  privacy: false,
  privacy_service: false,
});

// onMounted(() => {
//   const script = document.createElement("script");
//   script.src = "https://www.google.com/recaptcha/api.js";
//   document.head.appendChild(script);
// });

// const onSubmit = async (token) => {
//   if (await validateForm()) {
//     showSearching.value = true;

//     let formData = {};
//     for (let x in props.inputs) {
//       formData[x] = props.inputs[x]["value"];
//     }
//     formData.recaptchaToken = token;

//     await new Promise((resolve) => setTimeout(resolve, 1000));
//     Api(formData, disableSubmitBtn, apiErrorMessage);
//   }
// };

// window.onSubmit = onSubmit;

onMounted(() => {
  window.dataLayer?.push({
    event: "page_view",
  });

  const uncheckedCountries = ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NL", "PL", "PT", "RO", "SK", "SI", "ES", "SE", "AD", "BA", "IS", "LI", "MC", "MD", "ME", "MK", "NO", "RS", "CH", "UA", "GB", "VA", "SM"];

  if (props.inputs.device.value?.geo?.country_code && !uncheckedCountries.includes(props.inputs.device.value.geo.country_code)) {
    props.inputs.privacy.value = true;

    const privacyCheckbox = document.getElementById("privacyCheck");
    if (privacyCheckbox) {
      privacyCheckbox.checked = true;
    }
  }
});

const validateForm = async (event) => {
  // Push event to trigger GTM tag
  window.dataLayer?.push({
    event: "button_click",
    Click_Classes: "button_click", // Matches trigger condition "Click Classes contains button_click"
    _event: "gtm.linkClick", // Matches trigger condition "_event equals gtm.linkClick"
    _triggers: "203887914_4", // Matches trigger condition "_triggers matches RegEx"
    "gtm.element": event.target,
    "gtm.elementClasses": "button_click g-recaptcha btn border btn-outline-12 rounded-5 px-5",
    "gtm.elementId": "submit-button",
  });

  // Log for debugging
  console.log("GTM Event Pushed:", {
    event: "button_click",
    Click_Classes: "button_click",
    _event: "gtm.linkClick",
    _triggers: "203887914_4",
  });

  if (!validateUsername()) {
    return false;
  }
  if (!validateDOB()) {
    return false;
  }
  if (!validateEmail()) {
    return false;
  }
  if (!validatePassword()) {
    const alertMessage = validationMessages.value.password;
    Swal.fire({
      text: alertMessage,
      customClass: {
        popup: "custom-swal-popup",
      },
    });
    return false;
  }
  if (!validatePrivacy()) {
    const alertMessage = validationMessages.value.privacy;
    Swal.fire({
      text: alertMessage,
      customClass: {
        popup: "custom-swal-popup",
      },
    });
    return false;
  }

  // Show searching screen
  showSearching.value = true;

  // Prepare form data
  let formData = {};
  for (let x in props.inputs) {
    formData[x] = props.inputs[x]["value"];
  }

  // Wait for 3 seconds before making API call
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Make API call
  Api(formData, disableSubmitBtn, apiErrorMessage);

  return true;
};

const validatePrivacy = () => {
  if (props.inputs.privacy.value == false) {
    validationMessages.value.privacy = Language.alert_update;
    return false;
  } else {
    validationMessages.value.privacy = false;
    return true;
  }
};

// const validatePrivacyService = () => {
//   if (props.inputs.privacy_service.value == false) {
//     validationMessages.value.privacy_service = Language.alert_update;
//     return false;
//   } else {
//     validationMessages.value.privacy_service = false;
//     return true;
//   }
// };

const validatePassword = () => {
  let x = props.inputs.password.value;
  if (x.length > 14 || x.length < 6 || validator(x) == false || explicitValidator(x)) {
    validationMessages.value.password = Language.password_error_2;
    return false;
  } else {
    validationMessages.value.password = false;
    return true;
  }
};

const urlParams = new URLSearchParams(window.location.search);
const isUsernameZero = urlParams.get("username") === "all";

const validateUsername = () => {
  let x = props.inputs.username.value;
  const usernameRegex = /^(?=.*[0-9])(?=.*[a-zA-Z]).{6,14}$/;
  const usernameSimpleRegex = /^.{6,14}$/;

  if (isUsernameZero) {
    if (!usernameSimpleRegex.test(x)) {
      validationMessages.value.username = Language.username_error_3;
      return false;
    } else {
      validationMessages.value.username = false;
      return true;
    }
  } else {
    if (x.length > 14 || x.length < 6 || validator(x) == false || explicitValidator(x)) {
      validationMessages.value.username = Language.username_error_2;
      return false;
    } else {
      validationMessages.value.username = false;
      return true;
    }
  }
};

const validateEmail = () => {
  if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,})+$/.test(props.inputs.email.value)) {
    validationMessages.value.email = false;
    return true;
  } else {
    validationMessages.value.email = Language.email_error;
    return false;
  }
};

const validator = (str) => {
  return /[0-9][a-zA-Z]|[a-zA-Z][0-9]/.test(str);
};

const explicitValidator = (str) => {
  const format = /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/;
  return format.test(str);
};

const validateDOB = () => {
  if (props.inputs.birth_day.value == "" || props.inputs.birth_year.value == "" || props.inputs.birth_month.value == "") {
    validationMessages.value.dob = Language.select_your_age;
    return false;
  } else {
    validationMessages.value.dob = false;
    return true;
  }
};

const onFocus = () => {};
const onBlur = () => {};
const handleKeyPress = () => {};
</script>

<template>
  <Searching v-if="showSearching" />

  <div v-else class="card-container">
    <div class="glass-card">
      <div class="card-inner">
        <div class="logo-container">
          <div class="logo-bg">
            <img :src="assets + 'sexydates.png'" alt="Sexy Dates Logo" class="logo" />
          </div>
        </div>

        <div class="question-section">
          <h2 class="question-heading">{{ Language.password_create }}</h2>

          <div class="input-container">
            <input v-model="inputs.password.value" class="form-input" placeholder="Enter Your Password" type="password" @focus="onFocus" @blur="onBlur" @keypress="handleKeyPress" />

            <div class="legal-container">
              <div class="legal-text">
                <p>
                  <strong>{{ Language.basic_info }}</strong> <br />
                  <strong>{{ Language.data_controller }}</strong> {{ Language.leads }} <a class="info-link" href="/privacy">{{ Language.info }}</a> <br />
                  <strong>{{ Language.purpose }}</strong> {{ Language.data_response }} <a class="info-link" href="/privacy">{{ Language.info }}</a> <br />
                  <strong>{{ Language.rights }}</strong
                  >{{ Language.data_access }} <a class="info-link" href="/privacy">{{ Language.info }}</a> <br />
                  <strong>{{ Language.additional_info }}</strong
                  >{{ Language.data_protect }}
                  <a class="info-link" href="/privacy">{{ Language.privacy_policy }}</a>
                </p>
              </div>

              <div class="checkbox-container">
                <div class="checkbox-group">
                  <input type="checkbox" id="privacyCheck" v-model="inputs.privacy.value" class="custom-checkbox" />
                  <label for="privacyCheck" class="checkbox-label">{{ Language.privacy_agree }}</label>
                </div>

                <div
                  v-if="
                    [
                      'Los Angeles',
                      'San Diego',
                      'Santa Monica',
                      'San Francisco',
                      'San Jose',
                      'Fresno',
                      'Sacramento',
                      'Long Beach',
                      'Oakland',
                      'Bakersfield',
                      'Anaheim',
                      'Santa Ana',
                      'Riverside',
                      'Stockton',
                      'Chula Vista',
                      'Irvine',
                      'Fremont',
                      'San Bernardino',
                      'Modesto',
                      'Oxnard',
                      'Fontana',
                    ].includes(inputs.location.value)
                  "
                  class="checkbox-group"
                >
                  <input type="checkbox" id="flexCheckDefault" v-model="inputs.is_sellable.value" class="custom-checkbox" />
                  <label for="flexCheckDefault" class="checkbox-label">{{ Language.data_permission }}</label>
                </div>
              </div>
            </div>

            <button type="button" class="continue-btn" @click="(event) => validateForm(event)" :class="{ disabled: disableSubmitBtn }">
              {{ Language.btn_submit }}
              <div class="spinner-border spinner-grow-sm text-light" v-show="disableSubmitBtn" role="status">
                <span class="visually-hidden">{{ Language.loading }}</span>
              </div>
            </button>

            <p class="security-note">{{ Language.spam_alert }}</p>
          </div>
        </div>

        <div class="progress-container">
          <Steps :step="steps" />
          <div class="progress-bar-container">
            <div class="progress-bar-bg">
              <div class="progress-bar-fill" style="width: 100%"></div>
            </div>
            <div class="progress-text">7 of 7</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div id="alert-message3" style="display: none">
    {{ Language.alert_update }}
  </div>
  <div id="alert-message" style="display: none">
    {{ Language.alert_update }}
  </div>
  <div id="alert-message2" style="display: none">
    {{ Language.password_error_2 }}
  </div>
</template>

<style>
.card-container {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  min-height: 85vh;
  padding: 1rem;
  padding-left: 0;
}

.glass-card {
  width: 100%;
  max-width: 450px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 25px;
  box-shadow: 0 20px 35px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  position: relative;
  z-index: 10;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.glass-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 35px 60px rgba(0, 0, 0, 0.3);
}

.glass-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -50%;
  width: 200%;
  height: 100%;
  background: linear-gradient(60deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
  transform: rotate(25deg);
  transition: transform 0.8s ease;
}

.glass-card:hover::before {
  transform: rotate(25deg) translateX(30%);
}

.card-inner {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.logo-bg {
  background: rgba(255, 255, 255, 0.15);
  padding: 1rem;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.2);
}

.logo {
  width: 140px;
  height: auto;
  transition: transform 0.5s ease;
}

.logo-bg:hover .logo {
  transform: scale(1.05) rotate(5deg);
}

.question-section {
  background: rgba(0, 0, 0, 0.6);
  padding: 1.5rem;
  border-radius: 20px;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.3);
}

.question-heading {
  font-size: 1.5rem;
  font-weight: 600;
  color: #fff;
  text-align: center;
  margin-bottom: 1.8rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
}

.question-heading::after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: -10px;
  width: 50px;
  height: 3px;
  background: #fff;
  transform: translateX(-50%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.input-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.form-input {
  width: 100%;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  text-align: center;
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.form-input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.15);
}

.legal-container {
  width: 100%;
  margin: 0.5rem 0;
  padding: 0.5rem;
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.05);
}

.legal-text {
  font-size: 0.65rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
  line-height: 1.4;
}

.legal-text strong {
  color: white;
}

.info-link {
  color: #ff7eb3;
  text-decoration: none;
  transition: color 0.2s;
}

.info-link:hover {
  color: #7b68ee;
  text-decoration: underline;
}

.checkbox-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.checkbox-group {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.custom-checkbox {
  width: 18px;
  height: 18px;
  margin-top: 2px;
  cursor: pointer;
  accent-color: #ff7eb3;
}

.checkbox-label {
  font-size: 0.75rem;
  color: white;
  line-height: 1.3;
}

.security-note {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  text-align: center;
  max-width: 90%;
  margin: 0 auto;
}

.continue-btn {
  padding: 0.8rem 2.5rem;
  border-radius: 25px;
  border: none;
  background: linear-gradient(45deg, #7b68ee, #ff7eb3);
  color: white;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(123, 104, 238, 0.4);
  position: relative;
  overflow: hidden;
  width: fit-content;
  min-width: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.continue-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
  transition: all 0.5s ease;
}

.continue-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(123, 104, 238, 0.5);
}

.continue-btn:hover::before {
  left: 100%;
}

.continue-btn.disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.spinner-border {
  width: 1rem;
  height: 1rem;
}

.progress-container {
  margin-top: auto;
}

.progress-bar-container {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-bar-bg {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff7eb3, #7b68ee);
  border-radius: 10px;
  transition: width 1s ease;
  position: relative;
  overflow: hidden;
}

.progress-bar-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.6) 50%, rgba(255, 255, 255, 0.2) 100%);
  animation: shine 2s infinite linear;
}

.progress-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@media (max-width: 576px) {
  .card-inner {
    padding: 1.25rem;
  }

  .logo {
    width: 120px;
  }

  .glass-card {
    max-width: 90%;
  }

  .question-heading {
    font-size: 1.3rem;
  }

  .password-input,
  .form-input {
    font-size: 0.9rem;
    padding: 0.75rem;
  }

  .continue-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }

  .password-toggle {
    right: 10px;
  }

  .legal-text {
    font-size: 0.7rem;
  }

  .checkbox-label {
    font-size: 0.8rem;
  }
}
</style>
