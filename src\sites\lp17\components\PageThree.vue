<script setup>
import { ref, defineProps, inject } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Searching from "../../../sites/lp12/components//views/Searching.vue";
import config from "../../../assets/js/config/config.json";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location", "inputs"]);
const steps = ref(props.steps);
const assets = inject("assets");

const selectPreference = (preference) => {
  props.moveNextSlide(preference);
};

const cdnBase = config.cdn;
const imgPath = config.images.img;
const cardPhotoUrl = `${cdnBase}${imgPath}130.webp`;
</script>

<template>
  <div class="container insta-container">
    <div class="insta-card">
      <div class="insta-header">
        <img class="insta-avatar" :src="assets + 'sexydates.png'" alt="Profile" />
        <span class="insta-username">sexydates_official</span>
      </div>
      <div class="insta-photo">
        <img class="insta-photo-img" :src="cardPhotoUrl" alt="Instagram Photo" />
      </div>
      <div class="insta-content">
        <form>
          <div class="form-group">
            <label for="gender" class="w-100 text-center fs-5 insta-label">{{ Language.question_looking }}</label>
            <hr />
            <div class="d-flex justify-content-around my-3 mt-5">
              <div>
                <button type="button" class="btn insta-btn" for="option1" @click="selectPreference('men')"><img class="me-2" src="../../../assets/icons8-male.png" style="height: 24px" alt="" />{{ Language.guy }}</button>
              </div>
              <div>
                <button type="button" class="btn insta-btn" for="option2" @click="selectPreference('women')"><img class="me-2" src="../../../assets/icons8-female.png" style="height: 24px" alt="" />{{ Language.girl }}</button>
              </div>
            </div>
          </div>
        </form>
        <Steps :step="steps" />
        <div class="disclaimer mt-2">
          <div class="progress mb-3 insta-progress" role="progressbar" aria-label="Info example" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">
            <div class="progress-bar insta-bar" style="width: 42%"></div>
          </div>
          <div class="d-flex justify-content-center">
        <Searching :location="location" :language="Language" />
      </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.insta-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
}

.insta-card {
  background: #fff;
  border-radius: 1.2em;
  box-shadow: 0 4px 24px rgba(0,0,0,0.12);
  max-width: 340px;
  width: 100%;
  overflow: hidden;
  padding-bottom: 0.2em;
}

.insta-header {
  display: flex;
  align-items: center;
  padding: 1em 1em 0.5em 1em;
  background: #fff;
}
.insta-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e1306c;
  margin-right: 0.7em;
}
.insta-username {
  font-weight: 600;
  color: #262626;
  font-size: 1.1em;
}

.insta-photo {
  width: 100%;
  aspect-ratio: 1 / 0.8;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.insta-photo-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.insta-content {
  padding: 0.8em 1em 0.3em 1em;
}
.insta-label {
  font-weight: 500;
  color: #262626;
  font-size: 1.1em;
}
.insta-btn {
  width: 7em;
  height: 2.7em;
  border: none;
  border-radius: 1.2em;
  background: linear-gradient(90deg, #e1306c 0%, #fdc468 100%);
  color: #fff;
  font-weight: 600;
  font-size: 1em;
  box-shadow: 0 2px 8px rgba(225, 48, 108, 0.10);
  transition: background 0.2s, color 0.2s, transform 0.1s;
}
.insta-btn:hover {
  background: linear-gradient(90deg, #fdc468 0%, #e1306c 100%);
  color: #fff;
  transform: translateY(-2px) scale(1.04);
}
.insta-progress {
  background: #f7f7f7;
  border-radius: 1em;
}
.insta-bar {
  background: linear-gradient(90deg, #e1306c 0%, #fdc468 100%) !important;
}
.disclaimer {
  text-align: center;
  margin-top: 1em;
}
@media (max-width: 500px) {
  .insta-card {
    max-width: 98vw;
    border-radius: 0.7em;
  }
  .insta-content {
    padding: 0.7em 0.3em 0.3em 0.3em;
  }
  .insta-header {
    padding: 0.7em 0.5em 0.5em 0.5em;
  }
}
</style>
