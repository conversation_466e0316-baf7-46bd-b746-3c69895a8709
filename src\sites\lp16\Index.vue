<script setup>
import { ref, onMounted, provide, watch, defineAsyncComponent } from "vue";
import Axios from "axios";
// Use async component loading for better performance
const PageOne = defineAsyncComponent(() => import("../lp16/components/PageOne.vue"));
const PageTwo = defineAsyncComponent(() => import("../lp16/components/PageTwo.vue"));
const PageThree = defineAsyncComponent(() => import("../lp16/components/PageThree.vue"));
const PageFour = defineAsyncComponent(() => import("../lp16/components/PageFour.vue"));
const PageFive = defineAsyncComponent(() => import("../lp16/components/PageFive.vue"));
const PageSix = defineAsyncComponent(() => import("../lp16/components/PageSix.vue"));
const PageSeven = defineAsyncComponent(() => import("../lp16/components/PageSeven.vue"));
const PageEight = defineAsyncComponent(() => import("../lp16/components/PageEight.vue"));
import Navbar from "../lp16/components/Navbar.vue";
import Footer from "../../components/Footer12.vue";
import Background from "../../components/Background.vue";
import Endpoints from "./../../assets/js/config/endpoints.json";
import Params from "./../../assets/js/helper/urlParameters.js";
import Media from "./../../assets/js/helper/deviceDetection.js";
import Language from "./../../assets/js/helper/Language.js";
import inputData from "./../../assets/js/config/forms.json";
import config from "../../assets/js/config/config.json";
import Assets from "../../assets/js/config/config.json";

const cdn = Assets.cdn;

const location = ref();
const device = ref(false);
const steps = ref(1);
const hasEmailParam = ref(false);
const showLoader = ref(true);
const isAnimating = ref(false);
// Add cache for loaded components
const loadedComponents = ref({});
// Add a ref to store the container height
const containerHeight = ref(null);

provide("assets", config.assets);

// Transition methods
const onTransitionStart = () => {
  // When transition starts, store current height if not already set
  if (!containerHeight.value) {
    const container = document.querySelector(".animation-wrapper");
    if (container) {
      containerHeight.value = `${container.offsetHeight}px`;
    }
  }
};

const onTransitionEnd = () => {
  // When transition ends, we can reset the stored height
  setTimeout(() => {
    containerHeight.value = null;
  }, 50);
};

onMounted(async () => {
  // Preload critical assets
  const preloadAssets = () => {
    // Preload logo
    const logoImg = new Image();
    logoImg.src = config.assets + "sexydates.png";

    // Preload critical icons (if any)
    const iconsMale = new Image();
    iconsMale.src = "../../../assets/icons8-male.png";

    const iconsFemale = new Image();
    iconsFemale.src = "../../../assets/icons8-female.png";
  };

  // Start preloading assets
  preloadAssets();

  // Simulate loader for smoother experience
  setTimeout(() => {
    showLoader.value = false;
  }, 1000);

  // Use requestAnimationFrame for smoother transitions
  requestAnimationFrame(() => {
    document.body.classList.add("loaded");
  });

  const urlParams = new URLSearchParams(window.location.search);
  const emailParam = urlParams.get("email");

  hasEmailParam.value = !!emailParam;
  if (hasEmailParam.value) {
    inputData.email = {
      value: emailParam,
      valid: true,
      error: false,
      required: true,
    };
  }
  await Axios.get(Endpoints.geo).then((s) => {
    device.value = s.data;
    inputData.location.value = s.data.geo.city ? s.data.geo.city : "Nevada";
    inputData.device.value = s.data;
    inputData.lander.value = Params().path;
    inputData.click_id.value = Params().click_id;
    inputData.source_id.value = Params().source_id;
    inputData.country.value = s.data.geo.country;
    inputData.country_code.value = s.data.geo.country_code.toLowerCase();
    inputData.locale.value = Params().locale;
    inputData.media.value = Media().device;
    location.value = inputData.location.value;
    inputData.http.value = Params().http;
    inputData.t1.value = Params().t1;
    inputData.t2.value = Params().t2;
    inputData.l.value = Params().l;
    inputData.s3.value = Params().s3;
    inputData.cmp.value = Params().cmp;
    inputData.image.value = Params().image;
    inputData.traf_id.value = Params().traf_id;
    inputData.tsid.value = Params().tsid;
    inputData.alreadyRegistered.value = Params().alreadyRegistered;
    if (s.data.geo.postal_code) {
      inputData.postal_code.value = s.data.geo.postal_code;
    }
  });

  window.history.pushState({ page: 1 }, null);
  window.addEventListener("popstate", handlePopstate);

  function handlePopstate() {
    window.location.href = config.links.back_button + Params().url;
  }

  // Optimize rendering after initial load
  setTimeout(optimizeRender, 1500);
});

// Keep the GPU memory footprint low by using CSS containment
const optimizeRender = () => {
  // Apply containment to static elements
  const staticElements = document.querySelectorAll(".app-navbar, .app-footer, .gradient-overlay");
  staticElements.forEach((el) => {
    el.style.contain = "content";
  });

  // Optimize particles rendering
  const particles = document.querySelectorAll(".particle, .floating-heart");
  particles.forEach((el) => {
    el.style.contain = "layout style size";
    el.style.willChange = "transform, opacity";
  });
};

// Reduce the animation complexity for smoother transitions
const moveNextSlide = (preference) => {
  if (!isAnimating.value) {
    isAnimating.value = true;

    // Cancel any ongoing animations
    document
      .querySelector(".page-container")
      .getAnimations()
      .forEach((anim) => anim.cancel());

    // Use faster transitions for better performance
    setTimeout(() => {
      if (hasEmailParam.value && steps.value === 5) {
        steps.value = 7;
      } else {
        steps.value++;
      }

      // Shorter animation duration
      setTimeout(() => {
        isAnimating.value = false;
      }, 200);
    }, 200);
  }
};

// Similarly optimize moveBackSlide
const moveBackSlide = () => {
  if (!isAnimating.value) {
    isAnimating.value = true;

    // Cancel any ongoing animations
    document
      .querySelector(".page-container")
      .getAnimations()
      .forEach((anim) => anim.cancel());

    // Use faster transitions for better performance
    setTimeout(() => {
      if (hasEmailParam.value && steps.value === 7) {
        steps.value = 5;
      } else {
        steps.value--;
      }

      // Shorter animation duration
      setTimeout(() => {
        isAnimating.value = false;
      }, 200);
    }, 200);
  }
};

watch(steps, (newValue) => {
  console.log("Step changed to:", newValue);
});
</script>

<template>
  <div class="app-wrapper">
    <div class="animated-background">
      <div class="gradient-overlay"></div>
      <Background />
    </div>

    <!-- Preloader -->
    <div v-if="showLoader" class="preloader">
      <div class="heart-beat">
        <div class="heart"></div>
      </div>
      <p>Finding your perfect match...</p>
    </div>

    <div v-else class="app-content">
      <div class="glass-container">
        <Navbar class="app-navbar" />

        <main class="main-content">
          <div class="animation-wrapper" :style="containerHeight ? { height: containerHeight } : {}">
            <div class="page-container" :class="{ 'fade-out': isAnimating, 'fade-in': !isAnimating }">
              <transition name="slide-fade" mode="out-in" @before-leave="onTransitionStart" @after-enter="onTransitionEnd">
                <div v-if="steps === 1">
                  <PageOne :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
                </div>
                <div v-else-if="steps === 2">
                  <PageTwo :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
                </div>
                <div v-else-if="steps === 3">
                  <PageThree :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
                </div>
                <div v-else-if="steps === 4">
                  <PageFour :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
                </div>
                <div v-else-if="steps === 5">
                  <PageFive :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
                </div>
                <div v-else-if="steps === 6 && !hasEmailParam">
                  <PageSix :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
                </div>
                <div v-else-if="steps === 7 || (steps === 6 && hasEmailParam)">
                  <PageSeven :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
                </div>
                <div v-else-if="steps === 8">
                  <PageEight :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
                </div>
              </transition>
            </div>
          </div>

          <div class="floating-particles">
            <div class="particle particle-1"></div>
            <div class="particle particle-2"></div>
            <div class="particle particle-3"></div>
            <div class="particle particle-4"></div>
            <div class="particle particle-5"></div>
            <div class="particle particle-6"></div>
            <div class="particle particle-7 desktop-only"></div>
            <div class="particle particle-8 desktop-only"></div>
          </div>

          <div class="floating-hearts">
            <div class="floating-heart heart-1"></div>
            <div class="floating-heart heart-2"></div>
            <div class="floating-heart heart-3 desktop-only"></div>
          </div>
        </main>

        <Footer class="app-footer" :language="Language" />
      </div>
    </div>
  </div>
</template>

<style>
/* Global styles */
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Poppins", sans-serif;
  color: #fff;
  overflow-x: hidden;
  background-color: #f8f9fa;
}

body.loaded {
  animation: fadeIn 0.3s ease-in;
}

button,
input {
  font-family: "Poppins", sans-serif;
}

/* Hardware acceleration for smoother animations */
.app-wrapper,
.glass-container,
.animated-background,
.page-container,
.particle,
.floating-heart {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}
</style>

<style scoped>
.app-wrapper {
  position: relative;
  min-height: 100vh;
  width: 100%;
  overflow: hidden;
}

.animated-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ff508e, #7b68ee);
  z-index: -2;
}

.gradient-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0), rgba(0, 0, 0, 0.1));
  z-index: -1;
}

.app-content {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
  z-index: 1;
}

/* New glass container for full-screen effect */
.glass-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
  box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 1;
}

.glass-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: -50%;
  width: 200%;
  height: 100%;
  background: linear-gradient(60deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0) 100%);
  transform: rotate(25deg) translateY(-50%);
  pointer-events: none;
}

.app-navbar {
  position: relative;
  z-index: 10;
}

.main-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 1.5rem 0.5rem;
}

.animation-wrapper {
  position: relative;
  z-index: 5;
  width: 100%;
  display: flex;
  justify-content: center;
  min-height: 500px;
}

.page-container {
  max-width: 100%;
  width: 100%;
  position: relative;
  z-index: 2;
  transition: opacity 0.3s ease;
  height: 100%;
}

.fade-out {
  opacity: 0;
}

.fade-in {
  opacity: 1;
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
  contain: layout style paint;
  position: absolute;
  width: 100%;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(15px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-15px);
}

/* Optimized preloader */
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ff508e, #7b68ee);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  will-change: opacity;
  animation: fadeOut 0.3s ease forwards;
  animation-play-state: paused;
  transform: translateZ(0);
}

body:not(.loaded) .preloader {
  animation-play-state: running;
}

.heart-beat {
  position: relative;
  width: 80px; /* Reduced from 100px */
  height: 80px; /* Reduced from 100px */
  margin-bottom: 20px;
  will-change: transform;
  transform: translateZ(0);
}

.heart {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #ff508e;
  transform: rotate(45deg);
  animation: pulse 1.2s infinite cubic-bezier(0.215, 0.61, 0.355, 1); /* Optimized timing function */
  will-change: transform;
}

.heart::before,
.heart::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #ff508e;
  border-radius: 50%;
}

.heart::before {
  top: -50%;
  left: 0;
}

.heart::after {
  top: 0;
  left: -50%;
}

.preloader p {
  color: white;
  font-size: 1.2rem;
  font-weight: 500;
  margin-top: 1rem;
  animation: fadeInOut 2s infinite;
}

/* Floating particles */
.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
  z-index: 1;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: float 15s linear infinite;
  bottom: -10%;
  opacity: 0;
  will-change: transform, opacity;
  transform: translateZ(0);
  contain: layout style size;
}

.particle-1 {
  left: 10%;
  animation-delay: 0.5s;
}
.particle-2 {
  left: 20%;
  animation-delay: 1s;
  background: rgba(255, 80, 142, 0.4);
}
.particle-3 {
  left: 30%;
  animation-delay: 1.5s;
  width: 8px;
  height: 8px;
}
.particle-4 {
  left: 40%;
  animation-delay: 2s;
  background: rgba(123, 104, 238, 0.4);
}
.particle-5 {
  left: 50%;
  animation-delay: 2.5s;
  width: 4px;
  height: 4px;
}
.particle-6 {
  left: 60%;
  animation-delay: 3s;
  background: rgba(255, 80, 142, 0.4);
}
.particle-7 {
  left: 70%;
  animation-delay: 3.5s;
  width: 8px;
  height: 8px;
}
.particle-8 {
  left: 80%;
  animation-delay: 4s;
  background: rgba(123, 104, 238, 0.4);
}
.particle-9 {
  left: 90%;
  animation-delay: 4.5s;
  width: 4px;
  height: 4px;
}
.particle-10 {
  left: 95%;
  animation-delay: 5s;
  background: rgba(255, 80, 142, 0.4);
}

/* Floating hearts */
.floating-hearts {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
  z-index: 0;
  pointer-events: none;
}

.floating-heart {
  position: absolute;
  width: 15px;
  height: 15px;
  background-color: rgba(255, 255, 255, 0.2);
  transform: rotate(45deg);
  bottom: -10%;
  opacity: 0;
  animation: float-heart 20s linear infinite;
  will-change: transform, opacity;
  transform: translateZ(0);
  contain: layout style size;
}

.floating-heart::before,
.floating-heart::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.floating-heart::before {
  top: -50%;
  left: 0;
}

.floating-heart::after {
  top: 0;
  left: -50%;
}

.heart-1 {
  left: 15%;
  animation-delay: 1s;
  background-color: rgba(255, 80, 142, 0.15);
}
.heart-1::before,
.heart-1::after {
  background-color: rgba(255, 80, 142, 0.15);
}

.heart-2 {
  left: 35%;
  animation-delay: 5s;
  width: 20px;
  height: 20px;
}

.heart-3 {
  left: 55%;
  animation-delay: 8s;
  background-color: rgba(123, 104, 238, 0.15);
}
.heart-3::before,
.heart-3::after {
  background-color: rgba(123, 104, 238, 0.15);
}

.heart-4 {
  left: 75%;
  animation-delay: 3s;
  width: 25px;
  height: 25px;
  background-color: rgba(255, 80, 142, 0.1);
}
.heart-4::before,
.heart-4::after {
  background-color: rgba(255, 80, 142, 0.1);
}

.heart-5 {
  left: 90%;
  animation-delay: 7s;
  background-color: rgba(123, 104, 238, 0.1);
}
.heart-5::before,
.heart-5::after {
  background-color: rgba(123, 104, 238, 0.1);
}

@keyframes pulse {
  0% {
    transform: rotate(45deg) scale(0.8);
  }
  50% {
    transform: rotate(45deg) scale(1);
  }
  100% {
    transform: rotate(45deg) scale(0.8);
  }
}

@keyframes fadeInOut {
  0%,
  100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

@keyframes float {
  0% {
    transform: translateY(0);
    opacity: 0;
  }
  10% {
    opacity: 0.8;
  }
  90% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(-1000%);
    opacity: 0;
  }
}

@keyframes float-heart {
  0% {
    transform: translateY(0) rotate(45deg);
    opacity: 0;
  }
  10% {
    opacity: 0.5;
  }
  90% {
    opacity: 0.5;
  }
  100% {
    transform: translateY(-1000%) rotate(45deg);
    opacity: 0;
  }
}

@keyframes fadeOut {
  to {
    opacity: 0;
    visibility: hidden;
  }
}

.app-footer {
  position: relative;
  z-index: 5;
}

@media (min-width: 992px) {
  .page-container {
    max-width: 80%;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 0.75rem 0.25rem;
  }

  .floating-heart {
    display: none;
  }

  .glass-container {
    border-radius: 0;
    border: none;
  }

  .page-container {
    width: 100%;
  }

  .particle-7,
  .particle-8,
  .heart-3 {
    display: none;
  }

  .particle,
  .floating-heart {
    animation-duration: 25s; /* Slower animations on mobile = less CPU */
  }
}

@keyframes fadeIn {
  from {
    opacity: 0.6;
  }
  to {
    opacity: 1;
  }
}

/* Container for transition items */
.slide-fade-enter-active > div,
.slide-fade-leave-active > div {
  width: 100%;
}

/* Hide heavy elements on mobile */
.desktop-only {
  display: initial;
}

/* Move the card container to the start of the screen - Higher specificity */
.app-content .main-content .animation-wrapper .page-container .card-container {
  justify-content: flex-start !important;
  align-items: flex-start !important;
  padding-left: 0 !important;
}

.app-content .main-content .animation-wrapper .page-container .glass-card {
  margin-left: 0 !important;
  margin-right: auto !important;
  transform: translateX(0) !important;
}

@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }
}
</style>
