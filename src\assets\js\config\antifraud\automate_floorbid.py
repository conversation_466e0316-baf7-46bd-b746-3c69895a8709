import requests
import os
import json
import subprocess

# <PERSON>ron job required to run this script
# 0 2 * * * sudo /usr/bin/python3 /var/www/html/SEXY-DATES/src/assets/js/config/antifraud/automate_floorbid.py >> /var/log/automate_floorbid.log 2>&1


# Constants
API_URL = "https://trakle01.online/api/smartlink-epv"
RULES_DIR = "rules"  # Path to your rules directory
PROJECT_DIR = "/var/www/html/SEXY-DATES"  # Path to your project directory

def compare_and_update_prices():
    try:
        response = requests.get(API_URL)
        response.raise_for_status()
        datas = response.json()
    except requests.RequestException as e:
        print(f"API request failed: {e}")
        return

    for data in datas:
        country_code = data.get("country_code")
        epv = float(data.get("epv", 0))

        if not country_code:
            continue

        file_path = os.path.join(RULES_DIR, f"{country_code}.json")

        if not os.path.isfile(file_path):
            continue

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                country_data = json.load(f)

            current_price = float(country_data.get("price", 0))
            default_price = float(country_data.get("default_price", 0))

            if epv > current_price:
                country_data["price"] = round(epv * 1.10, 8)
            elif epv < current_price:
                country_data["price"] = default_price
            else:
                country_data["price"] = default_price

            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(country_data, f, indent=4)

        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            continue

    # Run npm build after updating JSON files
    try:
        print("Running npm run build...")
        subprocess.run(["npm", "run", "build"], cwd=PROJECT_DIR, check=True)
        print("Build completed successfully.")
    except subprocess.CalledProcessError as e:
        print(f"Error running npm run build: {e}")

if __name__ == "__main__":
    compare_and_update_prices()
