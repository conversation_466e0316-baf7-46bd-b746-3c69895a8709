<script setup>
import { ref, inject, onMounted } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Swal from "sweetalert2";
import Searching from "../../../sites/lp12/components//views/Searching.vue";

const props = defineProps({
  steps: { required: true },
  moveNextSlide: { required: true },
  moveBackSlide: { required: true },
  language: { default: () => ({}) },
  location: { default: "" },
  inputs: { required: true },
});
const steps = ref(props.steps);
const validEmail = ref(false);
const assets = inject("assets");

const validateEmail = () => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  validEmail.value = emailPattern.test(props.inputs.email.value);
};

const continueToNextSlide = () => {
  validateEmail();
  const alertMessage = document.getElementById("alert-message").innerText;

  if (!validEmail.value) {
    Swal.fire({
      text: alertMessage,
      customClass: {
        popup: "custom-swal-popup",
      },
    });
  } else {
    props.moveNextSlide();
  }
};

onMounted(() => {
  const urlParams = new URLSearchParams(window.location.search);
  const emailFromParams = urlParams.get("email");

  if (emailFromParams) {
    props.inputs.email.value = emailFromParams;
    setTimeout(() => {
      const emailInput = document.getElementById("email");
      if (emailInput) {
        emailInput.value = emailFromParams;
      }
    }, 0);
  }
});

const handleKeyPress = (event) => {
  if (event.key === "Enter") {
    continueToNextSlide();
  }
};

const onFocus = () => {};
const onBlur = () => {};
</script>

<template>
  <div class="card-container">
    <div class="glass-card">
      <div class="card-inner">
        <div class="logo-container">
          <div class="logo-bg">
            <img :src="assets + 'sexydates.png'" alt="Sexy Dates Logo" class="logo" />
          </div>
        </div>

        <div class="question-section">
          <h2 class="question-heading">{{ Language.email_text }}</h2>

          <div class="input-container">
            <input v-model="props.inputs.email.value" id="email" class="form-input" placeholder="e.g. <EMAIL>" type="text" @focus="onFocus" @blur="onBlur" @keypress="handleKeyPress" />

            <button type="button" class="continue-btn" @click="continueToNextSlide">
              {{ Language.continue }}
            </button>

            <p class="security-note">{{ Language.spam_alert }}</p>
          </div>
        </div>

        <div class="progress-container">
          <Steps :step="steps" />
          <div class="progress-bar-container">
            <div class="progress-bar-bg">
              <div class="progress-bar-fill" style="width: 84%"></div>
            </div>
            <div class="progress-text">6 of 7</div>
          </div>
          <div class="search-info">
            <Searching :location="location" :language="Language" />
          </div>
        </div>
      </div>
    </div>
  </div>

  <div id="alert-message" style="display: none">
    {{ Language.email_error }}
  </div>
</template>

<style>
.card-container {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  min-height: 85vh;
  padding: 1rem;
  padding-left: 0;
}

.glass-card {
  width: 100%;
  max-width: 450px;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 25px;
  box-shadow: 0 20px 35px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  position: relative;
  z-index: 10;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.glass-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 35px 60px rgba(0, 0, 0, 0.3);
}

.glass-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -50%;
  width: 200%;
  height: 100%;
  background: linear-gradient(60deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
  transform: rotate(25deg);
  transition: transform 0.8s ease;
}

.glass-card:hover::before {
  transform: rotate(25deg) translateX(30%);
}

.card-inner {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.logo-bg {
  background: rgba(255, 255, 255, 0.15);
  padding: 1rem;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.2);
}

.logo {
  width: 140px;
  height: auto;
  transition: transform 0.5s ease;
}

.logo-bg:hover .logo {
  transform: scale(1.05) rotate(5deg);
}

.search-info {
  margin-top: 1rem;
  text-align: center;
  color: #fff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 500;
}

.question-section {
  background: rgba(0, 0, 0, 0.6);
  padding: 1.5rem;
  border-radius: 20px;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.3);
}

.question-heading {
  font-size: 1.5rem;
  font-weight: 600;
  color: #fff;
  text-align: center;
  margin-bottom: 1.8rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
}

.question-heading::after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: -10px;
  width: 50px;
  height: 3px;
  background: #fff;
  transform: translateX(-50%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.input-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.form-input {
  width: 100%;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  text-align: center;
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.form-input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.15);
}

.security-note {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  text-align: center;
  max-width: 90%;
  margin: 0 auto;
}

.continue-btn {
  padding: 0.8rem 2.5rem;
  border-radius: 25px;
  border: none;
  background: linear-gradient(45deg, #7b68ee, #ff7eb3);
  color: white;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(123, 104, 238, 0.4);
  position: relative;
  overflow: hidden;
}

.continue-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);
  transition: all 0.5s ease;
}

.continue-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(123, 104, 238, 0.5);
}

.continue-btn:hover::before {
  left: 100%;
}

.progress-container {
  margin-top: auto;
}

.progress-bar-container {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-bar-bg {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff7eb3, #7b68ee);
  border-radius: 10px;
  transition: width 1s ease;
  position: relative;
  overflow: hidden;
}

.progress-bar-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.6) 50%, rgba(255, 255, 255, 0.2) 100%);
  animation: shine 2s infinite linear;
}

.progress-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@media (max-width: 576px) {
  .card-container {
    justify-content: center !important;
    align-items: flex-start !important;
    padding-left: 1rem !important;
    min-height: 100vh;
    padding: 0.5rem;
    padding-top: 2rem;
  }

  .glass-card {
    max-width: 95%;
    max-height: 75vh;
  }

  .card-inner {
    padding: 0.8rem;
  }

  .logo-container {
    margin-bottom: 0.5rem;
  }

  .logo {
    width: 80px;
  }

  .question-section {
    margin-bottom: 0.8rem;
    padding: 0.8rem;
  }

  .question-heading {
    font-size: 1.1rem;
    margin-bottom: 0.8rem;
  }

  .form-input {
    font-size: 0.85rem;
    padding: 0.6rem 0.8rem;
    margin-bottom: 0.5rem;
  }

  .continue-btn {
    padding: 0.6rem 1.5rem;
    font-size: 0.85rem;
  }

  .security-note {
    font-size: 0.75rem;
    margin-top: 0.5rem;
  }

  .progress-container {
    margin-top: 0.3rem;
  }

  .progress-bar-container {
    margin-top: 0.5rem;
  }

  .progress-text {
    font-size: 0.7rem;
    margin-top: 0.3rem;
  }
}
</style>
