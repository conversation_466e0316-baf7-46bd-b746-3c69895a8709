<script setup>
import { ref, onMounted, defineProps, computed } from "vue";
import Steps from "../../lp8/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Searching from "../../../sites/lp8/components/views/Searching.vue";
import config from "../../../assets/js/config/config.json";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location", "inputs"]);
const steps = ref(props.steps);

const selectGender = (gender) => {
  props.moveNextSlide(gender);
};

// Construct CDN URLs
const cdnBase = config.cdn;
const imgPath = config.images.img;
const getProfileImageUrl = (number) => {
  const adjustedNumber = number >= 12 ? number + 1 : number;
  return `${cdnBase}${imgPath}${adjustedNumber}.webp`;
};
const sideImageUrl = computed(() => `${cdnBase}${imgPath}118.webp`);

// Add countdown functionality
const minutes = ref(1);
const seconds = ref(50);

const formattedTime2 = computed(() => {
  return `${minutes.value} minute${minutes.value !== 1 ? "s" : ""} ${seconds.value} second${seconds.value !== 1 ? "s" : ""}`;
});

onMounted(() => {
  const timer2 = setInterval(() => {
    if (seconds.value > 0) {
      seconds.value--;
    } else if (minutes.value > 0) {
      minutes.value--;
      seconds.value = 56;
    } else {
      clearInterval(timer2);
    }
  }, 1000);
});
</script>

<template>
  <div class="one">
    <!-- Profile Gallery Container -->
    <div class="profile-gallery mt-2">
      <div class="gallery-container">
        <div class="profile-scroll-wrapper">
          <div v-for="n in 20" :key="n" class="profile-item">
            <div class="profile-image-container">
              <img :src="getProfileImageUrl((n % 30) + 1)" alt="Profile" class="profile-image" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Progress Bar -->
    <div class="progress-container">
      <div class="progress-wrapper">
        <div class="progress">
          <div class="progress-bar" role="progressbar" style="width: 40%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
    </div>

    <!-- Existing Content -->
    <div class="container mt-5">
      <div class="d-flex justify-content-center align-items-center">
        <!-- Image Container -->
        <div class="side-image-container">
          <img :src="sideImageUrl" alt="Side Image" class="side-image" />
          <div class="post-controls">
            <div class="post-actions">
              <div class="action-buttons">
                <button class="action-btn">
                  <i class="far fa-heart"></i>
                </button>
                <button class="action-btn">
                  <i class="far fa-comment"></i>
                </button>
                <button class="action-btn">
                  <i class="far fa-paper-plane"></i>
                </button>
              </div>
              <button class="action-btn bookmark">
                <i class="far fa-bookmark"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Content Container -->
        <div class="col-md-5 d-flex justify-content-end align-items-center">
          <div class="content-box" style="height: auto; width: 60rem">
            <!-- Notification Message -->
            <div class="notification-box mb-4">
              <div class="notification-content">
                <p class="timer-text mb-0">You still have {{ formattedTime2 }} to register!</p>
              </div>
            </div>
            <div class="clip">
              <h4 class="text-center fw-bold fs-6 text-primary shadow-8d">{{ Language.local_area }}</h4>
              <h3 class="text-center title-text mt-3">{{ Language.guy_girl }}</h3>
            </div>
            <div class="button-group d-flex justify-content-center gap-4 mt-3">
              <button type="button" class="btn decline-btn border fw-bold" @click="selectGender('female')">{{ Language.girl }}</button>
              <button type="button" class="btn accept-btn border fw-bold" @click="selectGender('male')">{{ Language.guy }}</button>
            </div>
            <Searching :location="location" :language="Language" />
          </div>
        </div>
      </div>
    </div>

    <Steps :step="steps" />
  </div>
</template>

<style>
.decline-btn {
  background-color: #dc3545 !important;
  width: 5rem;
  height: 5rem;
  color: #ffffff !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
}

.accept-btn {
  background-color: #28a745 !important;
  width: 7rem;
  height: 7rem;
  color: #ffffff !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.button-group button {
  border-radius: 50%;
  transition: all 0.3s ease-in-out;
}

.shadow-8d {
  font-size: 15px !important;
}

.button-group button:hover {
  transform: scale(1.1);
}

.title-text {
  color: #282d4f !important;
  font-size: 24px !important;
}

.text-primary {
  color: #282d4f !important;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.content-box {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 2em;
  border-radius: 14px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.side-image-container {
  position: relative;
  width: 400px;
  height: 500px;
  margin-right: 2rem;
  border-radius: 14px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  background-color: white;
}

.side-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.post-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px;
  background: white;
}

.post-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.action-btn {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  font-size: 1.5rem;
  color: #262626;
  transition: color 0.2s ease;
}

.action-btn:hover {
  color: #8e8e8e;
}

.action-btn.bookmark {
  font-size: 1.5rem;
}

@media only screen and (max-width: 1200px) {
  .side-image-container {
    width: 300px;
    height: 400px;
  }

  .action-btn {
    font-size: 1.2rem;
  }
}

@media only screen and (max-width: 867px) {
  .side-image-container {
    display: none;
  }

  .content-box {
    width: 21em !important;
    height: auto !important;
  }

  .title-text {
    font-size: 13px !important;
  }
}

/* Update profile gallery styles for better responsiveness */
.profile-gallery {
  width: 100%;
  background: linear-gradient(to right, #f8f9fa, #e9ecef);
  padding: 0.5rem 0;
  margin-bottom: 1rem;
  overflow: hidden; /* Add this to prevent horizontal overflow */
  z-index: 100;
}

.gallery-container {
  max-width: 100%;
  margin: 0 auto;
  position: relative;
}

.profile-scroll-wrapper {
  display: flex;
  overflow-x: auto;
  gap: 1rem; /* Reduce gap on small screens */
  padding: 0.5rem;
  scrollbar-width: none;
  -ms-overflow-style: none;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.profile-scroll-wrapper::-webkit-scrollbar {
  display: none;
}

.profile-item {
  position: relative;
  min-width: 60px; /* Reduce size for small screens */
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s;
}

.profile-item:hover {
  transform: translateY(-2px);
}

.profile-image-container {
  width: 65px; /* Smaller for mobile */
  height: 65px; /* Smaller for mobile */
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-number {
  position: absolute;
  bottom: 20px; /* Adjusted */
  left: 50%;
  transform: translateX(-50%);
  background-color: #fff;
  border-radius: 10px;
  padding: 1px 6px;
  font-size: 0.7rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.profile-name {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  color: #495057;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70px;
}

/* Add gradient fade effect on sides */
.gallery-container::before,
.gallery-container::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 50px;
  pointer-events: none;
  z-index: 2;
}

/* Add responsive media queries */
@media only screen and (max-width: 576px) {
  .profile-item {
    min-width: 50px;
  }

  .profile-image-container {
    width: 50px;
    height: 50px;
  }

  .profile-scroll-wrapper {
    gap: 0.5rem;
    padding: 0.25rem;
  }
}
</style>
