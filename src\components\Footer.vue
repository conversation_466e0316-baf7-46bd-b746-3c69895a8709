<script setup>
import { ref, onMounted } from "vue";
import axios from "axios";

defineProps(["language", "state"]);

const email = ref("");
const isSubmitting = ref(false);
const statusMessage = ref("");
const statusType = ref("");
const userCountry = ref("");
const showCookieConsent = ref(!localStorage.getItem("cookiesAccepted"));

const submitEmail = async () => {
  if (!email.value) {
    setStatus("Please enter an email address.", "error");
    return;
  }

  isSubmitting.value = true;
  setStatus("", "");

  try {
    const response = await axios.get(`https://trakle01.online/api/email-unsubscribe/${email.value}`);
    if (response.data.success) {
      setStatus("You have been successfully unsubscribed.", "success");
      email.value = "";
    } else {
      setStatus("Email not found in our database.", "error");
    }
  } catch (error) {
    console.error("Error:", error);
    setStatus("An error occurred. Please try again later.", "error");
  } finally {
    isSubmitting.value = false;
  }
};

const setStatus = (message, type) => {
  statusMessage.value = message;
  statusType.value = type;
};

const resetForm = () => {
  email.value = "";
  setStatus("", "");
};

const fetchUserCountry = async () => {
  try {
    const response = await axios.get("https://trakle01.online/api/device");
    userCountry.value = response.data.geo.state;
  } catch (error) {
    console.error("Error fetching country:", error);
  }
};

const acceptCookies = () => {
  localStorage.setItem("cookiesAccepted", "true");
  showCookieConsent.value = false;
  window.dataLayer = window.dataLayer || [];
  window.dataLayer.push({
    event: "cookieConsent",
    action: "accept",
  });
};

const ignoreCookies = () => {
  showCookieConsent.value = false;
  window.dataLayer = window.dataLayer || [];
  window.dataLayer.push({
    event: "cookieConsent",
    action: "reject",
  });
};

onMounted(() => {
  fetchUserCountry();
});
</script>

<template>
  <div class="row t-bg align-items-end col-12">
    <footer class="mt-2 pt-3 col-md-8 t-bg-black mx-auto">
      <ul class="nav justify-content-center mb-0">
        <li class="nav-item text-center px-4">
          <p>
            <i class="fa-solid text-warning fa-shield fs-6 icons"></i> <br />
            <label class="pt-1 text-success">
              <small>{{ language.secure }}</small>
            </label>
          </p>
        </li>
        <li class="nav-item text-center px-4">
          <p>
            <i class="fa-solid text-warning fa-shield-halved fs-6 icons"></i> <br />
            <label class="pt-1 text-success">
              <small>{{ language.private }}</small>
            </label>
          </p>
        </li>
        <li class="nav-item text-center px-4">
          <p>
            <i class="fa-brands text-warning fa-expeditedssl fs-6 icons"></i> <br />
            <label class="pt-1 text-success">
              <small>{{ language.safe }}</small>
            </label>
          </p>
        </li>
        <li class="nav-item text-center px-4">
          <p>
            <i class="fa-brands text-warning fa-cc-visa fs-6 icons"></i> <br />
            <label class="pt-1 text-success">
              <small>{{ language.payment }}</small>
            </label>
          </p>
        </li>
      </ul>
      <ul class="nav justify-content-center border-bottom pb-1 mb-0" style="font-size: 0.7em">
        <li class="nav-item">
          <a href="/privacy" target="_blank" class="nav-link px-2 text-success">{{ language.privacy }}</a>
        </li>
        <li class="nav-item">
          <a href="/terms" target="_blank" class="nav-link px-2 text-success">{{ language.terms }}</a>
        </li>
        <li class="nav-item">
          <a href="/legal" target="_blank" class="nav-link px-2 text-success">{{ language.legal_notice }}</a>
        </li>
        <li class="nav-item">
          <a href="/ccpa" target="_blank" class="nav-link px-2 text-success" v-if="userCountry === 'California'">{{ language.ccpa }}</a>
        </li>
        <li class="nav-item">
          <a href="#" style="height: 1.5em; cursor: pointer" data-bs-toggle="modal" data-bs-target="#emailunsub" class="nav-link px-2 text-success">{{ language.unsubscribe }}</a>
        </li>
      </ul>
      <p class="text-center text-success mt-1" style="font-size: 0.7em">Sexy-Dates &copy; Copyright 2024</p>
    </footer>
  </div>

  <!-- Cookie Consent Message -->
  <div v-if="showCookieConsent" class="alert alert-light fixed-bottom mb-0">
    <div class="container text-center">
      <p class="mb-0 text-sm text-center">
        We use cookies and other tracking technologies to improve your browsing experience on our website, to show you personalized content and targeted ads, to analyze our website traffic, and to understand where our visitors are coming from.
        <button class="alert-button me-2" @click="ignoreCookies">REJECT ALL</button>
        <button class="alert-button" @click="acceptCookies">ACCEPT ALL</button>
      </p>
    </div>
  </div>
  <!-- Unsubscribe Modal -->
  <div class="modal fade" id="emailunsub" tabindex="-1" aria-labelledby="emailunsub" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{{ language.unsubscribe }}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" @click="resetForm"></button>
        </div>
        <div class="modal-body">
          <div class="subhead">
            <p class="text-center">{{ language.to_sub }}</p>
          </div>
          <div class="emailinput d-flex justify-content-center"><input v-model="email" id="emailInput" style="width: 25em" type="email" placeholder="Enter your email" required /></div>
          <div v-if="statusMessage" :class="['mt-3', 'text-center', { 'text-success': statusType === 'success', 'text-danger': statusType === 'error' }]">
            {{ statusMessage }}
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-sm btn-danger" data-bs-dismiss="modal" @click="resetForm">Close</button>
          <button @click="submitEmail" type="button" class="btn btn-sm btn-dark" id="submitEmail" :disabled="isSubmitting">
            {{ isSubmitting ? "Submitting..." : "Submit" }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.t-bg {
  background-color: rgba(255, 255, 255, 0.5);
}

.alert {
  padding: 0.3rem;
  font-size: 0.6em;
}

.alert-button {
  height: 2em !important;
  font-size: 10px;
  border-radius: 5px;
  color: wheat;
  background-color: black;
}

@media (max-width: 767px) {
  .row.t-bg.align-items-end.col-12 {
    margin-top: 8px !important;
  }

  .alert {
    padding: 0.2rem;
    font-size: 0.3em;
  }

  .alert-button {
    height: 1.5em !important;
    width: auto;
    font-size: 7px;
    border-radius: 5px;
    color: wheat;
    background-color: black;
    text-align: center;
    line-height: 0.5em; /* Match line-height with button height */
    padding-bottom: 1.2px; /* Remove any padding that might affect height */
  }
}
</style>
