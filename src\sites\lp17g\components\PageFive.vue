<script setup>
import { ref, defineProps, inject } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Params from "../../../assets/js/helper/urlParameters.js";
import Language from "../../../assets/js/helper/Language.js";
import Searching from "../../../sites/lp12/components//views/Searching.vue";
import config from "../../../assets/js/config/config.json";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location", "inputs"]);
const steps = ref(props.steps);
const selectedAge = ref(null);
const assets = inject("assets");

const validationMessages = ref({ dob: null });

const handleAgeButtonClick = (ageGroup) => {
  selectedAge.value = ageGroup;
  if (ageGroup === "18-34") {
    window.location.href = "https://trakle02.online/tracker/100" + Params().url;
  }
};
const dob = (x, y) => {
  let min = parseInt(x);
  let max = parseInt(y);
  let random = Math.floor(Math.random() * (parseInt(max) - parseInt(min))) + min;
  props.inputs.birth_year.value = new Date().getFullYear() - (random + 1);
  props.inputs.birth_month.value = (Math.floor(Math.random() * (parseInt(12) - parseInt(1))) + 1).toString();
  props.inputs.birth_day.value = (Math.floor(Math.random() * (parseInt(29) - parseInt(1))) + 1).toString();

  props.inputs.birth_day.value.length == 1 ? (props.inputs.birth_day.value = "0" + props.inputs.birth_day.value) : "";
  props.inputs.birth_month.value.length == 1 ? (props.inputs.birth_month.value = "0" + props.inputs.birth_month.value) : "";

  props.moveNextSlide();
};

const cdnBase = config.cdn;
const imgPath = config.images.img;
const cardPhotoUrl = `${cdnBase}${imgPath}122.webp`;
</script>

<template>
  <div class="container insta-container">
    <div class="insta-card">
      <div class="insta-header">
        <img class="insta-avatar" :src="assets + 'sexydates.png'" alt="Profile" />
      </div>
      <div class="insta-photo">
        <img class="insta-photo-img" :src="cardPhotoUrl" alt="Instagram Photo" />
      </div>
      <div class="insta-content">
        <form>
          <div class="form-group">
            <label for="gender" class="w-100 text-center fs-5 insta-label">{{ Language.age_question }}</label>
            <hr />
            <div class="d-flex age-btn-row my-3 mt-5">
              <button type="button" class="btn insta-btn px-4 18-btn" for="option2" @click="handleAgeButtonClick('18-34')">18+</button>
              <button type="button" class="btn insta-btn px-4 35-btn" for="option2" @click="dob(35, 40)">35+</button>
              <button type="button" class="btn insta-btn px-4 40-btn" for="option2" @click="dob(40, 64)">40+</button>
              <button type="button" class="btn insta-btn px-4 65-btn" for="option2" @click="dob(65, 75)">65+</button>
            </div>
          </div>
        </form>
        <p v-if="validationMessages.dob" class="text-light fs-6 mt-2">({{ validationMessages.dob }})</p>
        <Steps :step="steps" />
        <div class="disclaimer mt-5">
          <div class="progress mb-3 insta-progress" role="progressbar" aria-label="Info example" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">
            <div class="progress-bar insta-bar" style="width: 70%"></div>
          </div>
          <div class="d-flex justify-content-center">
            <Searching :location="location" :language="Language" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.insta-container {
  min-height: 85vh;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  background: transparent;
  padding-top: 1rem;
}

.insta-card {
  background: #fff;
  border-radius: 1.2em;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
  width: 340px;
  /* width: 100%; */
  overflow: hidden;
  padding-bottom: 0.2em;
}

.insta-header {
  display: flex;
  align-items: center;
  padding: 1em 1em 0.5em 1em;
  background: #fff;
}
.insta-avatar {
  width: 150px;
  height: auto;
  object-fit: cover;
  margin-right: 0.7em;
}
.insta-username {
  font-weight: 600;
  color: #262626;
  font-size: 1.1em;
}

.insta-photo {
  width: 100%;
  aspect-ratio: 1 / 0.8;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.insta-photo-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.insta-content {
  padding: 0.8em 1em 0.3em 1em;
}
.insta-label {
  font-weight: 500;
  color: #262626;
  font-size: 1.1em;
}
.insta-btn {
  width: 4.5em;
  height: 2em;
  border: none;
  border-radius: 1.2em;
  background: linear-gradient(90deg, #e1306c 0%, #fdc468 100%);
  color: #fff;
  font-weight: 600;
  font-size: 0.93em;
  box-shadow: 0 2px 8px rgba(225, 48, 108, 0.1);
  transition: background 0.2s, color 0.2s, transform 0.1s;
  padding: 0.2em 0.5em;
  white-space: nowrap;
}
.insta-btn:hover {
  background: linear-gradient(90deg, #fdc468 0%, #e1306c 100%);
  color: #fff;
  transform: translateY(-2px) scale(1.04);
}
.insta-progress {
  background: #f7f7f7;
  border-radius: 1em;
}
.insta-bar {
  background: linear-gradient(90deg, #e1306c 0%, #fdc468 100%) !important;
}
.disclaimer {
  text-align: center;
  margin-top: 1em;
}
.age-btn-row {
  flex-wrap: nowrap;
  justify-content: center !important;
  gap: 0.5em !important;
}
@media (max-width: 500px) {
  .insta-container {
    padding-top: 1rem;
    min-height: 100vh;
  }
  .insta-card {
    max-width: 98vw;
    border-radius: 0.7em;
  }
  .insta-content {
    padding: 0.7em 0.3em 0.3em 0.3em;
  }
  .insta-header {
    padding: 0.7em 0.5em 0.5em 0.5em;
  }
  .age-btn-row {
    flex-wrap: wrap;
  }
  .insta-btn {
    width: 5em;
  }
}
</style>
