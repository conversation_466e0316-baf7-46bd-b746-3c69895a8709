<script setup>
import { ref, onMounted, defineProps, computed } from "vue";
import Steps from "../../lp8/components/views/StepsMarker.vue";
import Searching from "../../../sites/lp8/components/views/Searching.vue";
import Language from "../../../assets/js/helper/Language.js";
import config from "../../../assets/js/config/config.json";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location"]);
const steps = ref(props.steps);
const selectPreference = (preference) => {
  props.moveNextSlide(preference);
};

// Construct CDN URLs
const cdnBase = config.cdn;
const imgPath = config.images.img;
const getProfileImageUrl = (number) => {
  const adjustedNumber = number >= 12 ? number + 1 : number;
  return `${cdnBase}${imgPath}${adjustedNumber}.webp`;
};
const sideImageUrl = computed(() => `${cdnBase}${imgPath}116.webp`);

// Add countdown functionality
const minutes = ref(1);
const seconds = ref(54);

const formattedTime = computed(() => {
  return `${minutes.value} minute${minutes.value !== 1 ? "s" : ""} ${seconds.value} second${seconds.value !== 1 ? "s" : ""}`;
});

onMounted(() => {
  const timer = setInterval(() => {
    if (seconds.value > 0) {
      seconds.value--;
    } else if (minutes.value > 0) {
      minutes.value--;
      seconds.value = 59;
    } else {
      clearInterval(timer);
    }
  }, 1000);
});
</script>

<template>
  <div class="one">
    <!-- Profile Gallery Container -->
    <div class="profile-gallery mt-2">
      <div class="gallery-container">
        <div class="profile-scroll-wrapper">
          <div v-for="n in 20" :key="n" class="profile-item">
            <div class="profile-image-container">
              <img :src="getProfileImageUrl((n % 30) + 1)" alt="Profile" class="profile-image" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="progress-section">
      <div class="progress-container">
        <div class="progress-wrapper">
          <div class="progress">
            <div class="progress-bar" role="progressbar" style="width: 20%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Existing Content -->
    <div class="container mt-5">
      <div class="d-flex justify-content-center align-items-center">
        <!-- Image Container -->
        <div class="side-image-container">
          <img :src="sideImageUrl" alt="Side Image" class="side-image" />
          <div class="post-controls">
            <div class="post-actions">
              <div class="action-buttons">
                <button class="action-btn">
                  <i class="far fa-heart"></i>
                </button>
                <button class="action-btn">
                  <i class="far fa-comment"></i>
                </button>
                <button class="action-btn">
                  <i class="far fa-paper-plane"></i>
                </button>
              </div>
              <button class="action-btn bookmark">
                <i class="far fa-bookmark"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Notification Message -->
        <div class="col-md-5 d-flex justify-content-end align-items-center">
          <div class="content-box" style="height: auto; width: 60rem">
            <div class="d-flex justify-content-center align-items-center mb-3">
              <a class="text-warning text-decoration-none" href="https://go.blcdog.com/smartpop/40ed1cc1cf7b3714debca75a15f868161c8a9b68c6450191968ef0ed7836daf4?userId=39b80d1f53f8ed833ed76bfeac5b2b16bebe56489fe5bf8f2e4e923766b2e58f&memberId=&p1=page&sourceId=youcontrolher.com_tab">
                <img src="../../../assets/fire.gif" alt="gif" style="width: 30px" />
                <span style="text-decoration: underline; display: inline-block; width: fit-content">Control her on cam!!</span>
                <img src="../../../assets/fire.gif" alt="gif" style="width: 30px" />
              </a>
            </div>
            <!-- Notification Message -->
            <div class="notification-box mb-4">
              <div class="notification-content">
                <p class="timer-text mb-0">You still have {{ formattedTime }} to register!</p>
              </div>
            </div>

            <div class="clip">
              <h4 class="text-center fw-bold fs-6 text-primary shadow-8d">{{ Language.local_area }}</h4>
              <h3 class="text-center title-text mt-3">{{ Language.over_18 }}</h3>
            </div>
            <div class="button-group d-flex justify-content-center gap-4 mt-3">
              <button type="button" class="btn decline-btn border fw-bold" @click="selectPreference('no')">
                {{ Language.no }}
              </button>
              <button type="button" class="btn accept-btn border fw-bold" @click="selectPreference('yes')">
                {{ Language.yes_i_am }}
              </button>
            </div>
            <Searching :location="location" :language="Language" />
          </div>
        </div>
      </div>
    </div>

    <Steps :step="steps" />
  </div>
</template>

<style>
.decline-btn {
  background-color: #dc3545 !important;
  width: 5rem;
  height: 5rem;
  color: #ffffff !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
}

.accept-btn {
  background-color: #28a745 !important;
  width: 7rem;
  height: 7rem;
  color: #ffffff !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.button-group {
  display: flex;
  justify-content: center;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  2%,
  6% {
    transform: translateX(-4px);
  }
  4%,
  8% {
    transform: translateX(4px);
  }
  10% {
    transform: translateX(0);
  }
}

.button-group button {
  border-radius: 50%;
  transition: all 0.3s ease-in-out;
}

.button-group button:hover {
  transform: scale(1.1);
}

.title-text {
  color: #282d4f !important;
  font-size: 24px !important;
}

.text-primary {
  color: #282d4f !important;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.content-box {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 2em;
  border-radius: 14px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  animation: shake 4s infinite;
}

.side-image-container {
  position: relative;
  width: 400px;
  height: 500px;
  margin-right: 2rem;
  border-radius: 14px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  background-color: white;
  animation: floatCard 3s ease-in-out infinite;
}

/* Add floating animation for the side image card */
@keyframes floatCard {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.side-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.post-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px;
  background: white;
}

.post-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.action-btn {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  font-size: 1.5rem;
  color: #262626;
  transition: color 0.2s ease;
}

.action-btn:hover {
  color: #8e8e8e;
}

.action-btn.bookmark {
  font-size: 1.5rem;
}

@media only screen and (max-width: 1200px) {
  .side-image-container {
    width: 300px;
    height: 400px;
  }

  .action-btn {
    font-size: 1.2rem;
  }
}

@media only screen and (max-width: 867px) {
  .side-image-container {
    display: none;
  }

  .content-box {
    width: 21em !important;
    height: auto !important;
    padding: 1em !important;
  }

  .title-text {
    font-size: 13px !important;
  }

  .container {
    padding-left: 0.25rem !important;
    padding-right: 0.25rem !important;
    margin-top: 0.5rem !important;
  }

  .profile-gallery {
    margin-bottom: 0.25rem;
  }

  .profile-scroll-wrapper {
    padding: 0.15rem;
  }

  .profile-item {
    margin-right: 0.25rem;
  }

  .notification-box {
    padding: 6px 8px;
    margin-bottom: 0.5rem;
  }

  .progress-section {
    margin-bottom: 0.15rem;
  }
}

/* Base profile gallery styles */
.profile-gallery {
  width: 100%;
  overflow: hidden;
  margin-bottom: 1rem;
  padding: 0 0.5rem;
}

.gallery-container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  position: relative;
}

.gallery-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.shadow-8d {
  font-size: 15px !important;
}

.profile-scroll-wrapper {
  display: flex;
  padding: 0.5rem;
  gap: 0.75rem;
  width: max-content;
}

.profile-item {
  flex: 0 0 auto;
  width: 70px;
}

.profile-image-container {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-image-container:hover {
  transform: scale(1.05);
}

/* Mobile Adjustments */
@media (max-width: 768px) {
  .profile-gallery {
    padding: 0 0.25rem;
    margin-bottom: 0.5rem;
    margin-top: 0 !important;
  }

  .profile-scroll-wrapper {
    padding: 0.25rem;
    gap: 0.5rem;
  }

  .profile-image-container {
    width: 65px;
    height: 65px;
  }

  .profile-item {
    width: 65px;
  }
}

/* Small Mobile Adjustments */
@media (max-width: 375px) {
  .profile-gallery {
    padding: 0 0.25rem;
  }

  .profile-scroll-wrapper {
    gap: 0.4rem;
  }

  .profile-image-container {
    width: 60px;
    height: 60px;
  }

  .profile-item {
    width: 60px;
  }
}

/* Add fade effect on sides */
.gallery-container::before,
.gallery-container::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 32px;
  pointer-events: none;
  z-index: 2;
}

.gallery-container::before {
  left: 0;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0));
}

.gallery-container::after {
  right: 0;
  background: linear-gradient(to left, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0));
}

.profile-name {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  color: #495057;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70px;
}

.stats-container {
  width: 100%;
  padding: 1rem 0;
  margin-top: 2rem;
  background: transparent !important;
  box-shadow: none;
}

.stats-wrapper {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 1.5rem;
  border-radius: 8px;
  transition: transform 0.2s ease;
  background: transparent;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 2rem;
  color: #4a90e2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: transparent;
  border-radius: 50%;
}

.stat-content {
  text-align: left;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #4a90e2;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 1rem;
  color: #4a90e2;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

@media only screen and (max-width: 768px) {
  .stats-wrapper {
    flex-direction: column;
    gap: 1.5rem;
  }

  .stat-item {
    width: 100%;
    justify-content: center;
    padding: 0.25rem 1rem;
  }

  .stat-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
  }

  .stat-value {
    font-size: 1.25rem;
  }

  .stat-label {
    font-size: 0.875rem;
  }
}

/* Update hover effect to work with the breathing animation */
.profile-item:hover .profile-image-container {
  animation: none;
  transform: scale(1.1);
}

.notification-box {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px 20px;
  margin-bottom: 1.5rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.notification-content p {
  margin: 0;
}

.notification-content p:first-child {
  color: #333;
  font-size: 0.95rem;
}

.timer-text {
  color: #db1d86;
  font-weight: bold;
  font-size: 1rem;
  margin-top: 4px;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

/* Progress Bar Styles */
.progress-section {
  width: 100%;
  background-color: #f8f9fa;
  position: sticky;
  top: 0;
  z-index: 100;
}

.progress-container {
  width: 100%;
  padding: 0.5rem;
  background: white;
}

.progress-wrapper {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
}

.progress {
  height: 4px;
  background-color: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
  background: linear-gradient(90deg, #4169e1 0%, #800080 100%);
  transition: width 0.3s ease;
}

.step-text {
  text-align: center;
  color: #666;
  font-size: 0.9rem;
  padding: 0.5rem;
  background-color: white;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .progress-container {
    padding: 0.3rem;
  }

  .progress-wrapper {
    padding: 0 0.5rem;
  }

  .progress {
    height: 3px;
  }

  .step-text {
    font-size: 0.8rem;
    padding: 0.4rem;
  }

  /* Adjust main content to account for fixed progress bar */
  .one {
    padding-top: 0.25rem !important;
  }

  /* Adjust profile gallery position */
  .profile-gallery {
    margin-top: 0 !important;
  }

  .profile-gallery.mt-2 {
    margin-top: 0 !important;
  }

  .progress-section {
    margin-bottom: 0.25rem;
  }
}

/* Small Mobile Styles */
@media (max-width: 375px) {
  .progress-container {
    padding: 0.25rem;
  }

  .progress-wrapper {
    padding: 0 0.25rem;
  }

  .progress {
    height: 2px;
  }

  .step-text {
    font-size: 0.75rem;
    padding: 0.3rem;
  }
}

/* Handle notched displays */
@supports (padding-top: env(safe-area-inset-top)) {
  .progress-section {
    padding-top: env(safe-area-inset-top);
  }
}

/* Handle landscape mode */
@media (max-width: 768px) and (orientation: landscape) {
  .progress-section {
    position: sticky;
  }

  .progress-container {
    padding: 0.3rem 0.5rem;
  }

  .step-text {
    padding: 0.2rem;
  }
}
</style>
