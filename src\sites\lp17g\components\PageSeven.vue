<script setup>
import { ref, defineProps, onMounted, inject } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Api from "../../../assets/js/helper/api.js";
import Swal from "sweetalert2";
import Searching from "../../lp14/components/views/Searching.vue";
import config from "../../../assets/js/config/config.json";

const props = defineProps(["inputs", "steps", "moveNextSlide"]);
const steps = ref(props.steps);
const apiErrorMessage = ref(false);
const disableSubmitBtn = ref(false);
const assets = inject("assets");
const showSearching = ref(false);

const validationMessages = ref({
  username: false,
  dob: false,
  email: false,
  password: false,
  privacy: false,
  privacy_service: false,
});

// onMounted(() => {
//   const script = document.createElement("script");
//   script.src = "https://www.google.com/recaptcha/api.js";
//   document.head.appendChild(script);
// });

// const onSubmit = async (token) => {
//   if (await validateForm()) {
//     showSearching.value = true;

//     let formData = {};
//     for (let x in props.inputs) {
//       formData[x] = props.inputs[x]["value"];
//     }
//     formData.recaptchaToken = token;

//     await new Promise((resolve) => setTimeout(resolve, 1000));
//     Api(formData, disableSubmitBtn, apiErrorMessage);
//   }
// };

// window.onSubmit = onSubmit;

onMounted(() => {
  showSearching.value = false;
  window.dataLayer?.push({
    event: "page_view",
  });

  const uncheckedCountries = ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NL", "PL", "PT", "RO", "SK", "SI", "ES", "SE", "AD", "BA", "IS", "LI", "MC", "MD", "ME", "MK", "NO", "RS", "CH", "UA", "GB", "VA", "SM"];

  if (props.inputs.device.value?.geo?.country_code && !uncheckedCountries.includes(props.inputs.device.value.geo.country_code)) {
    props.inputs.privacy.value = true;

    const privacyCheckbox = document.getElementById("privacyCheck");
    if (privacyCheckbox) {
      privacyCheckbox.checked = true;
    }
  }
});

const validateForm = async (event) => {
  // Push event to trigger GTM tag
  window.dataLayer?.push({
    event: "button_click",
    Click_Classes: "button_click", // Matches trigger condition "Click Classes contains button_click"
    _event: "gtm.linkClick", // Matches trigger condition "_event equals gtm.linkClick"
    _triggers: "203887914_4", // Matches trigger condition "_triggers matches RegEx"
    "gtm.element": event.target,
    "gtm.elementClasses": "button_click g-recaptcha btn border btn-outline-12 rounded-5 px-5",
    "gtm.elementId": "submit-button",
  });

  // Log for debugging
  console.log("GTM Event Pushed:", {
    event: "button_click",
    Click_Classes: "button_click",
    _event: "gtm.linkClick",
    _triggers: "203887914_4",
  });

  if (!validateUsername()) {
    return false;
  }
  if (!validateDOB()) {
    return false;
  }
  if (!validateEmail()) {
    return false;
  }
  if (!validatePassword()) {
    const alertMessage = validationMessages.value.password;
    Swal.fire({
      text: alertMessage,
      customClass: {
        popup: "custom-swal-popup",
      },
    });
    return false;
  }
  if (!validatePrivacy()) {
    const alertMessage = validationMessages.value.privacy;
    Swal.fire({
      text: alertMessage,
      customClass: {
        popup: "custom-swal-popup",
      },
    });
    return false;
  }

  // Show searching screen
  showSearching.value = true;

  // Prepare form data
  let formData = {};
  for (let x in props.inputs) {
    formData[x] = props.inputs[x]["value"];
  }

  // Wait for 3 seconds before making API call
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Make API call
  Api(formData, disableSubmitBtn, apiErrorMessage);

  return true;
  e;
};

const validatePrivacy = () => {
  if (props.inputs.privacy.value == false) {
    validationMessages.value.privacy = Language.alert_update;
    return false;
  } else {
    validationMessages.value.privacy = false;
    return true;
  }
};

// const validatePrivacyService = () => {
//   if (props.inputs.privacy_service.value == false) {
//     validationMessages.value.privacy_service = Language.alert_update;
//     return false;
//   } else {
//     validationMessages.value.privacy_service = false;
//     return true;
//   }
// };

const validatePassword = () => {
  let x = props.inputs.password.value;
  if (x.length > 14 || x.length < 6 || validator(x) == false || explicitValidator(x)) {
    validationMessages.value.password = Language.password_error_2;
    return false;
  } else {
    validationMessages.value.password = false;
    return true;
  }
};

const urlParams = new URLSearchParams(window.location.search);
const isUsernameZero = urlParams.get("username") === "all";

const validateUsername = () => {
  let x = props.inputs.username.value;
  const usernameRegex = /^(?=.*[0-9])(?=.*[a-zA-Z]).{6,14}$/;
  const usernameSimpleRegex = /^.{6,14}$/;

  if (isUsernameZero) {
    if (!usernameSimpleRegex.test(x)) {
      validationMessages.value.username = Language.username_error_3;
      return false;
    } else {
      validationMessages.value.username = false;
      return true;
    }
  } else {
    if (x.length > 14 || x.length < 6 || validator(x) == false || explicitValidator(x)) {
      validationMessages.value.username = Language.username_error_2;
      return false;
    } else {
      validationMessages.value.username = false;
      return true;
    }
  }
};

const validateEmail = () => {
  if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,})+$/.test(props.inputs.email.value)) {
    validationMessages.value.email = false;
    return true;
  } else {
    validationMessages.value.email = Language.email_error;
    return false;
  }
};

const validator = (str) => {
  return /[0-9][a-zA-Z]|[a-zA-Z][0-9]/.test(str);
};

const explicitValidator = (str) => {
  const format = /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/;
  return format.test(str);
};

const validateDOB = () => {
  if (props.inputs.birth_day.value == "" || props.inputs.birth_year.value == "" || props.inputs.birth_month.value == "") {
    validationMessages.value.dob = Language.select_your_age;
    return false;
  } else {
    validationMessages.value.dob = false;
    return true;
  }
};

const cdnBase = config.cdn;
const imgPath = config.images.img;
const cardPhotoUrl = `${cdnBase}${imgPath}149.webp`;
</script>

<template>
  <Searching v-if="showSearching" />
  <div v-else class="container insta-container">
    <div class="insta-card">
      <div class="insta-header">
        <img class="insta-avatar" :src="assets + 'sexydates.png'" alt="Profile" />
      </div>
      <div class="insta-photo">
        <img class="insta-photo-img" :src="cardPhotoUrl" alt="Instagram Photo" />
      </div>
      <div class="insta-content">
        <form id="demo-form">
          <div class="form-group">
            <label for="gender" class="w-100 text-center fs-5 insta-label">{{ Language.password_create }}</label>
            <hr />
            <div class="d-flex justify-content-around my-2">
              <div class="d-flex justify-content-center flex-column gap-3 w-100">
                <input v-model="props.inputs.password.value" class="form-control rounded-5 px-5" placeholder="Enter Your Password" type="password" @focus="onFocus" @blur="onBlur" @keypress="handleKeyPress" />
                <div class="others">
                  <div class="d-flex flex-column align-items-center justify-content-center">
                    <div class="notice">
                      <p>
                        <strong> {{ Language.basic_info }}</strong> <br />
                        <strong>{{ Language.data_controller }}</strong> {{ Language.leads }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.info }}</a> <br />
                        <strong>{{ Language.purpose }}</strong> {{ Language.data_response }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.info }}</a> <br />
                        <strong>{{ Language.rights }}</strong
                        >{{ Language.data_access }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.info }}</a> <br />
                        <strong>{{ Language.additional_info }}</strong
                        >{{ Language.data_protect }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.privacy_policy }}</a>
                      </p>
                    </div>
                    <div class="accept_agreed">
                      <div class="d-flex align-items-start mb-1">
                        <input type="checkbox" id="privacyCheck" v-model="props.inputs.privacy.value" class="custom-checkbox mt-1" />
                        <label for="privacyCheck" class="agree text-dark ms-2">{{ Language.privacy_agree }}</label>
                      </div>
                    </div>
                    <div
                      v-if="
                        [
                          'Los Angeles',
                          'San Diego',
                          'Santa Monica',
                          'San Francisco',
                          'San Jose',
                          'Fresno',
                          'Sacramento',
                          'Long Beach',
                          'Oakland',
                          'Bakersfield',
                          'Anaheim',
                          'Santa Ana',
                          'Riverside',
                          'Stockton',
                          'Chula Vista',
                          'Irvine',
                          'Fremont',
                          'San Bernardino',
                          'Modesto',
                          'Oxnard',
                          'Fontana',
                        ].includes(props.inputs.location.value)
                      "
                      class="text-center mt-3"
                    >
                      <input type="checkbox" id="flexCheckDefault" v-model="props.inputs.is_sellable.value" />
                      <label class="fs-6 ms-2" for="flexCheckDefault">{{ Language.data_permission }}</label>
                    </div>
                  </div>
                </div>
                <button type="button" class="g-recaptcha btn insta-btn w-100 final-submit-btn" @click="(event) => validateForm(event)" :class="`${disableSubmitBtn ? 'disabled' : ''}`">
                  {{ Language.btn_submit }}
                  <div class="spinner-border spinner-grow-sm text-light" v-show="disableSubmitBtn" role="status">
                    <span class="visually-hidden">{{ Language.loading }}</span>
                  </div>
                </button>
                <div class="text-center">
                  <p class="text-dark" style="font-size: 10px">{{ Language.spam_alert }}</p>
                </div>
              </div>
            </div>
          </div>
        </form>
        <div id="alert-message3" style="display: none">
          {{ Language.alert_update }}
        </div>
        <div id="alert-message" style="display: none">
          {{ Language.alert_update }}
        </div>
        <div id="alert-message2" style="display: none">
          {{ Language.password_error_2 }}
        </div>
        <Steps :step="steps" />
        <div class="disclaimer mt-2">
          <div class="progress mb-3 insta-progress" role="progressbar" aria-label="Info example" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">
            <div class="progress-bar insta-bar" style="width: 100%"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.insta-container {
  min-height: 85vh;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  background: transparent;
  padding-top: 1rem;
}

.insta-card {
  background: #fff;
  border-radius: 1.2em;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
  width: 340px;
  /* width: 100%; */
  overflow: hidden;
  padding-bottom: 0.2em;
}

.insta-header {
  display: flex;
  align-items: center;
  padding: 1em 1em 0.5em 1em;
  background: #fff;
}
.insta-avatar {
  width: 150px;
  height: auto;
  object-fit: cover;
  margin-right: 0.7em;
}
.insta-username {
  font-weight: 600;
  color: #262626;
  font-size: 1.1em;
}

.insta-photo {
  width: 100%;
  aspect-ratio: 1 / 0.8;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.insta-photo-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.insta-content {
  padding: 0.8em 1em 0.3em 1em;
}
.insta-label {
  font-weight: 500;
  color: #262626;
  font-size: 1.1em;
}
.insta-btn {
  width: 7em;
  height: 2.7em;
  border: none;
  border-radius: 1.2em;
  background: linear-gradient(90deg, #e1306c 0%, #fdc468 100%);
  color: #fff;
  font-weight: 600;
  font-size: 1em;
  box-shadow: 0 2px 8px rgba(225, 48, 108, 0.1);
  transition: background 0.2s, color 0.2s, transform 0.1s;
}
.insta-btn:hover {
  background: linear-gradient(90deg, #fdc468 0%, #e1306c 100%);
  color: #fff;
  transform: translateY(-2px) scale(1.04);
}
.insta-progress {
  background: #f7f7f7;
  border-radius: 1em;
}
.insta-bar {
  background: linear-gradient(90deg, #e1306c 0%, #fdc468 100%) !important;
}
.disclaimer {
  text-align: center;
  margin-top: 1em;
}
.notice {
  font-size: 7px !important;
}
.accept_agreed .form-check-input {
  width: 20px;
  height: 20px;
}
.accept_agreed {
  font-size: 13px !important;
}
@media (max-width: 500px) {
  .insta-container {
    padding-top: 1rem;
    min-height: 100vh;
  }
  .insta-card {
    max-width: 98vw;
    border-radius: 0.7em;
  }
  .insta-content {
    padding: 0.7em 0.3em 0.3em 0.3em;
  }
  .insta-header {
    padding: 0.7em 0.5em 0.5em 0.5em;
  }
}
</style>
