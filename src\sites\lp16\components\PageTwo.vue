<script setup>
import { ref, inject } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Searching from "../../../sites/lp12/components//views/Searching.vue";

const props = defineProps({
  steps: { required: true },
  moveNextSlide: { required: true },
  moveBackSlide: { required: true },
  language: { default: () => ({}) },
  location: { default: "" },
  inputs: { required: true },
});
const steps = ref(props.steps);
const assets = inject("assets");

const selectGender = (gender) => {
  props.moveNextSlide(gender);
};
</script>

<template>
  <div class="card-container">
    <div class="glass-card">
      <div class="card-inner">
        <div class="logo-container">
          <div class="logo-bg">
            <img :src="assets + 'sexydates.png'" alt="Sexy Dates Logo" class="logo" />
          </div>
        </div>

        <div class="question-section">
          <h2 class="question-heading">{{ Language.guy_girl }}</h2>

          <div class="answer-options">
            <button class="answer-btn left-btn" @click="selectGender('female')">
              <img class="gender-icon" src="../../../assets/icons8-female.png" alt="" />
              <span class="btn-text">{{ Language.girl }}</span>
            </button>

            <div class="divider">
              <span class="divider-text">or</span>
            </div>

            <button class="answer-btn right-btn" @click="selectGender('male')">
              <img class="gender-icon" src="../../../assets/icons8-male.png" alt="" />
              <span class="btn-text">{{ Language.guy }}</span>
            </button>
          </div>
        </div>

        <div class="progress-container">
          <Steps :step="steps" />
          <div class="progress-bar-container">
            <div class="progress-bar-bg">
              <div class="progress-bar-fill" style="width: 28%"></div>
            </div>
            <div class="progress-text">2 of 7</div>
          </div>
          <div class="search-info">
            <Searching :location="location" :language="Language" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.card-container {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  min-height: 85vh;
  padding: 1rem;
  padding-left: 0;
}

.glass-card {
  width: 100%;
  max-width: 450px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 25px;
  box-shadow: 0 20px 35px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  position: relative;
  z-index: 10;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.glass-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 35px 60px rgba(0, 0, 0, 0.3);
}

.glass-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -50%;
  width: 200%;
  height: 100%;
  background: linear-gradient(60deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
  transform: rotate(25deg);
  transition: transform 0.8s ease;
}

.glass-card:hover::before {
  transform: rotate(25deg) translateX(30%);
}

.card-inner {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.logo-bg {
  background: rgba(255, 255, 255, 0.15);
  padding: 1rem;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.2);
}

.logo {
  width: 140px;
  height: auto;
  transition: transform 0.5s ease;
}

.logo-bg:hover .logo {
  transform: scale(1.05) rotate(5deg);
}

.search-info {
  margin-top: 1rem;
  text-align: center;
  color: #fff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 500;
}

.question-section {
  background: rgba(0, 0, 0, 0.6);
  padding: 1.5rem;
  border-radius: 20px;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.3);
}

.question-heading {
  font-size: 1.5rem;
  font-weight: 600;
  color: #fff;
  text-align: center;
  margin-bottom: 1.8rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
}

.question-heading::after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: -10px;
  width: 50px;
  height: 3px;
  background: #fff;
  transform: translateX(-50%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.answer-options {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.divider {
  width: 1px;
  height: 50px;
  background: rgba(255, 255, 255, 0.3);
  position: relative;
}

.divider-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.3);
  color: white;
  padding: 0.3rem 0.7rem;
  border-radius: 50%;
  font-size: 0.8rem;
  white-space: nowrap;
}

.answer-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 45%;
  padding: 1rem;
  border: none;
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.answer-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  z-index: -1;
  transition: width 0.5s ease;
}

.left-btn {
  box-shadow: 0 4px 15px rgba(255, 80, 80, 0.3);
}

.left-btn::before {
  background: linear-gradient(45deg, #ff5050, #ff8080);
}

.right-btn {
  box-shadow: 0 4px 15px rgba(76, 217, 100, 0.3);
}

.right-btn::before {
  background: linear-gradient(45deg, #4cd964, #68e080);
}

.answer-btn:hover::before {
  width: 100%;
}

.answer-btn:hover {
  transform: translateY(-5px);
  color: white;
}

.gender-icon {
  height: 24px;
  width: auto;
  margin-bottom: 0.5rem;
  filter: brightness(10);
}

.btn-text {
  font-size: 1.1rem;
}

.progress-container {
  margin-top: auto;
}

.progress-bar-container {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-bar-bg {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff7eb3, #7b68ee);
  border-radius: 10px;
  transition: width 1s ease;
  position: relative;
  overflow: hidden;
}

.progress-bar-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.6) 50%, rgba(255, 255, 255, 0.2) 100%);
  animation: shine 2s infinite linear;
}

.progress-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@media (max-width: 576px) {
  .card-container {
    justify-content: center !important;
    align-items: flex-start !important;
    padding-left: 1rem !important;
    min-height: 100vh;
    padding: 0.5rem;
    padding-top: 2rem;
  }

  .glass-card {
    max-width: 95%;
    max-height: 75vh;
  }

  .card-inner {
    padding: 0.8rem;
  }

  .logo-container {
    margin-bottom: 0.5rem;
  }

  .logo {
    width: 80px;
  }

  .question-section {
    margin-bottom: 0.8rem;
    padding: 0.8rem;
  }

  .question-heading {
    font-size: 1.1rem;
    margin-bottom: 0.8rem;
  }

  .answer-options {
    flex-direction: column;
    gap: 0.6rem;
  }

  .answer-btn {
    width: 100%;
    padding: 0.6rem 1.5rem;
    font-size: 0.85rem;
  }

  .divider {
    width: 80%;
    height: 1px;
    margin: 0.2rem 0;
  }

  .btn-text {
    font-size: 0.85rem;
  }

  .progress-container {
    margin-top: 0.3rem;
  }

  .progress-bar-container {
    margin-top: 0.5rem;
  }

  .progress-text {
    font-size: 0.7rem;
    margin-top: 0.3rem;
  }
}

@media (max-width: 768px) {
  .card-container {
    justify-content: center !important;
    align-items: center !important;
    padding-left: 1rem !important;
  }
}
</style>
